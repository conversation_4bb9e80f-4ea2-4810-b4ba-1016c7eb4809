﻿using System;
using System.Diagnostics;
using System.IO;
using uBuyFirst.Other;

namespace uBuyFirst.SkuManager
{
    internal class PythonProcessManager
    {
        private static Process? s_internalSkuManagerProcess = null;
        private static int s_internalSkuManagerProcessId = 0;

        internal static bool IsInternalSkuManagerScriptRunning()
        {
            if (s_internalSkuManagerProcessId > 0)
            {
                return true;
            }
            return false;
        }

        internal static void StopInternalSkuManagerScript()
        {
            var killedByHandle = false;

            // First try to kill using stored process handle
            if (s_internalSkuManagerProcess != null)
            {
                try
                {
                    if (!s_internalSkuManagerProcess.HasExited)
                    {
                        s_internalSkuManagerProcess.Kill();
                        // Wait for process to fully terminate
                        if (s_internalSkuManagerProcess.WaitForExit(1000))
                        {
                            killedByHandle = true;
                            Debug.WriteLine("Process terminated and waited for port release");
                        }
                        else
                        {
                            Debug.WriteLine("Process did not exit within timeout");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error killing Internal Sku Manager process by handle: {ex.Message}");
                }
                finally
                {
                    s_internalSkuManagerProcess = null;
                }
            }

            // As fallback, find and kill any python processes running uvicorn
            if (!killedByHandle)
            {
                // First try to kill by process ID if we have it
                if (s_internalSkuManagerProcessId > 0)
                {
                    try
                    {
                        var processById = Process.GetProcessById(s_internalSkuManagerProcessId);
                        if (processById != null && !processById.HasExited)
                        {
                            processById.Kill();
                            if (processById.WaitForExit(1000))
                            {
                                killedByHandle = true;
                                Debug.WriteLine($"Killed Python process by stored ID: {s_internalSkuManagerProcessId} and waited for port release");
                            }
                            else
                            {
                                Debug.WriteLine($"Process {s_internalSkuManagerProcessId} did not exit within timeout");
                            }
                        }
                    }
                    catch (ArgumentException)
                    {
                        // Process not found by ID, which is fine
                        Debug.WriteLine($"Process with ID {s_internalSkuManagerProcessId} not found");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error killing process by ID {s_internalSkuManagerProcessId}: {ex.Message}");
                    }
                    finally
                    {
                        s_internalSkuManagerProcessId = 0;
                    }
                }

                // As final fallback, look for Python processes in our venv
                if (!killedByHandle)
                {
                    var pythonProcesses = Process.GetProcessesByName("python");
                    var venvPythonPath = Path.Combine(Folders.SkuManagerScripts, "venv", "Scripts", "python.exe");

                    Debug.WriteLine($"Looking for Python processes matching path: {venvPythonPath}");
                    foreach (var process in pythonProcesses)
                    {
                        try
                        {
                            var modulePath = process.MainModule?.FileName;
                            Debug.WriteLine($"Found Python process (PID: {process.Id}) at: {modulePath}");

                            if (modulePath != null &&
                                string.Equals(modulePath, venvPythonPath, StringComparison.OrdinalIgnoreCase))
                            {
                                process.Kill();
                                if (process.WaitForExit(1000))
                                {
                                    Debug.WriteLine($"Killed python process running uvicorn (PID: {process.Id}) and waited for port release");
                                }
                                else
                                {
                                    Debug.WriteLine($"Process {process.Id} did not exit within timeout");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error checking/killing python process: {ex.Message}");
                        }
                        finally
                        {
                            process.Dispose();
                        }
                    }
                }
            }
        }

        public static int StartInternalSkuManagerScript()
        {
            var startInfo = new ProcessStartInfo();
            startInfo.WorkingDirectory = Folders.SkuManagerScripts;
            startInfo.CreateNoWindow = true;
            startInfo.UseShellExecute = false;
            // Construct the full path to python.exe within the venv
            var venvPythonPath = Path.Combine(Folders.SkuManagerScripts, "venv", "Scripts", "python.exe");
            startInfo.FileName = venvPythonPath;
            startInfo.WindowStyle = ProcessWindowStyle.Hidden;
            // Use -m to run the uvicorn module with the venv's python
            startInfo.Arguments = "-m uvicorn internalSkuScript:app --host 0.0.0.0";

            try
            {
                // Start the process and store the handle
                s_internalSkuManagerProcess = Process.Start(startInfo);
                if (s_internalSkuManagerProcess != null)
                {
                    s_internalSkuManagerProcessId = s_internalSkuManagerProcess.Id;
                    Debug.WriteLine($"Started Internal Sku Manager process with ID: {s_internalSkuManagerProcessId}");
                    return s_internalSkuManagerProcessId;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error starting Internal Sku Manager process: {ex.Message}");
                s_internalSkuManagerProcess = null;
                s_internalSkuManagerProcessId = 0;
            }

            return 0;
        }
    }
}
